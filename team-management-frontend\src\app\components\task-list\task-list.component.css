.task-card {
  background-color: white;
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
  position: relative;
  cursor: move;
  margin-bottom: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
}

.task-card.priority-high {
  border-left-color: #dc3545;
}

.task-card.priority-medium {
  border-left-color: #ffc107;
}

.task-card.priority-low {
  border-left-color: #0dcaf0;
}

.completed-task {
  opacity: 0.7;
}

.task-description {
  max-height: 3em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 10px;
}

.task-list {
  min-height: 50px;
  max-height: 500px;
  overflow-y: auto;
  padding: 8px;
  border-radius: 4px;
}

/* Styles pour les en-têtes de colonnes */
.card-header.bg-primary {
  background: linear-gradient(45deg, #007bff, #6610f2) !important;
}

.card-header.bg-warning {
  background: linear-gradient(45deg, #ffc107, #fd7e14) !important;
}

.card-header.bg-success {
  background: linear-gradient(45deg, #28a745, #20c997) !important;
}

/* Styles pour le drag and drop */
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;
  opacity: 0.8;
}

.cdk-drag-placeholder {
  opacity: 0.3;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.task-list.cdk-drop-list-dragging .task-card:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Poignée de glisser-déposer */
.task-drag-handle {
  position: absolute;
  bottom: 5px;
  right: 5px;
  color: #adb5bd;
  cursor: move;
  font-size: 0.8rem;
}

/* Styles pour les colonnes Kanban */
.kanban-column {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.kanban-column-header {
  padding: 15px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.kanban-column-content {
  flex-grow: 1;
  background-color: #f8f9fa;
  padding: 15px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  min-height: 300px;
  max-height: 600px;
  overflow-y: auto;
}
