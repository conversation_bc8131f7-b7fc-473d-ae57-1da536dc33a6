<!-- Navbar moderne avec design élégant -->
<nav class="navbar navbar-expand-lg navbar-light fixed-top shadow-sm py-3"
     [ngClass]="{'navbar-scrolled': isScrolled}" id="mainNav">
  <div class="container px-4">
    <!-- Logo et nom de l'application -->
    <a class="navbar-brand fw-bold d-flex align-items-center" routerLink="/">
      <i class="bi bi-people-fill text-primary me-2 fs-3"></i>
      <span class="logo-text">Team Management</span>
    </a>

    <!-- Bouton hamburger pour mobile -->
    <button class="navbar-toggler rounded-3 border-0 shadow-none" type="button" data-bs-toggle="collapse"
            data-bs-target="#navbarResponsive" aria-controls="navbarResponsive"
            aria-expanded="false" aria-label="Toggle navigation">
      <i class="bi bi-list fs-2"></i>
    </button>

    <!-- Menu de navigation -->
    <div class="collapse navbar-collapse" id="navbarResponsive">
      <ul class="navbar-nav ms-auto me-4 my-3 my-lg-0">
        <li class="nav-item mx-1">
          <a class="nav-link px-3 py-2 rounded-pill"
             routerLink="/equipes/liste"
             routerLinkActive="active-link">
            <i class="bi bi-people me-1"></i> Équipes
          </a>
        </li>
        <li class="nav-item mx-1">
          <a class="nav-link px-3 py-2 rounded-pill"
             routerLink="/utilisateurs"
             routerLinkActive="active-link">
            <i class="bi bi-person me-1"></i> Utilisateurs
          </a>
        </li>
      </ul>

      <!-- Bouton de profil (exemple) -->
      <div class="dropdown">
        <button class="btn btn-light rounded-circle p-2 shadow-sm dropdown-toggle no-caret"
                type="button" id="profileDropdown" data-bs-toggle="dropdown"
                aria-expanded="false" title="Profil">
          <i class="bi bi-person-circle fs-5"></i>
        </button>
        <ul class="dropdown-menu dropdown-menu-end shadow border-0 rounded-3 mt-2"
            aria-labelledby="profileDropdown">
          <li><h6 class="dropdown-header">Mon compte</h6></li>
          <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>Paramètres</a></li>
          <li><a class="dropdown-item" href="#"><i class="bi bi-question-circle me-2"></i>Aide</a></li>
          <li><hr class="dropdown-divider"></li>
          <li><a class="dropdown-item text-danger" (click)="logout()" style="cursor: pointer;"><i class="bi bi-box-arrow-right me-2"></i>Déconnexion</a></li>
        </ul>
      </div>
    </div>
  </div>
</nav>

<!-- Espace pour compenser la navbar fixed-top -->
<div class="navbar-spacer"></div>

<!-- Styles spécifiques pour la navbar -->
<style>
  /* Styles pour la navbar */
  #mainNav {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  /* Style pour la navbar après défilement */
  .navbar-scrolled {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
    background-color: rgba(255, 255, 255, 0.98) !important;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  /* Espace pour compenser la navbar fixed-top */
  .navbar-spacer {
    height: 76px;
  }

  /* Style pour les liens actifs */
  .active-link {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd !important;
    font-weight: 500;
  }

  /* Animation au survol des liens */
  .nav-link {
    transition: all 0.2s ease;
    position: relative;
  }

  .nav-link:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: translateY(-1px);
  }

  /* Style pour le logo */
  .logo-text {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
  }

  /* Style pour le bouton de profil */
  .no-caret::after {
    display: none;
  }

  /* Animation pour le dropdown */
  .dropdown-menu {
    animation: fadeIn 0.2s ease;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
</style>
